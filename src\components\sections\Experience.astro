---
import type { Experience, Education, Certification } from "@/data/portfolio";
import { sectionTitles } from "@/data/portfolio";
import { Calculator, GraduationCap, Users, TrendingUp, Building2 } from "lucide-vue-next";

interface Props {
    sectionTitle: string;
    employment: Experience[];
    education: Education[];
    certifications: Certification[];
}

const { sectionTitle, employment, education, certifications } = Astro.props;

// Helper function to get the appropriate icon component for certifications
const getCertificationIconComponent = (iconType: any) => {
    // Since we can't directly compare component references in Astro,
    // we'll use the icon name or a mapping approach
    if (iconType === Calculator) return Calculator;
    if (iconType === GraduationCap) return GraduationCap;
    if (iconType === Users) return Users;
    if (iconType === TrendingUp) return TrendingUp;
    if (iconType === Building2) return Building2;
    return Calculator; // fallback
};
---

<section id="experience" aria-labelledby="exp-title" class="py-10 border-t border-slate-200">
    <h2 id="exp-title" class="text-2xl text-sky-900 font-extrabold">{sectionTitle}</h2>

    <div class="mt-6 space-y-8">
        <!-- Employment Experience -->
        {
            employment.length > 0 && (
                <section aria-labelledby="employment-title">
                    <h3 id="employment-title" class="text-sky-900 font-bold">
                        {sectionTitles.employmentExperience}
                    </h3>
                    <div class="mt-3 grid gap-4">
                        {employment.map((job) => (
                            <article class="bg-white border border-slate-200 rounded p-4">
                                <div class="flex items-start gap-4">
                                    {/* Logo Placeholder */}
                                    <div class="flex-shrink-0" set:html={job.logoPlaceholder} />

                                    <div class="flex-1 min-w-0">
                                        {/* PRIMARY DATA: Institution/Organization name + Position title (bold and prominent) */}
                                        <h4 class="text-sky-900 font-bold text-lg mb-1">{job.organization}</h4>
                                        <h5 class="text-sky-900 font-bold text-base mb-2">{job.title}</h5>

                                        {/* SECONDARY DATA: Location, Years, Employment type (smaller and less prominent) */}
                                        <div class="text-sm text-slate-600 mb-2 space-y-1">
                                            <p>
                                                <span class="font-medium">Period:</span> {job.period}
                                            </p>
                                            <p>
                                                <span class="font-medium">Location:</span> {job.location}
                                            </p>
                                            <p>
                                                <span class="font-medium">Employment Type:</span> {job.employmentType}
                                            </p>
                                        </div>

                                        <ul class="list-disc pl-5 mt-2 text-sm space-y-1">
                                            {job.responsibilities.map((responsibility) => (
                                                <li>{responsibility}</li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Academic Qualifications -->
        {
            education.length > 0 && (
                <section aria-labelledby="degrees-title" class="border-t border-slate-100 pt-6">
                    <h3 id="degrees-title" class="text-sky-900 font-bold">
                        {sectionTitles.academicQualifications}
                    </h3>
                    <div class="mt-3 grid gap-4">
                        {education.map((degree) => (
                            <article class="bg-white border border-slate-200 rounded p-4">
                                <div class="flex items-start gap-4">
                                    {/* Logo Placeholder */}
                                    <div class="flex-shrink-0" set:html={degree.logoPlaceholder} />

                                    <div class="flex-1 min-w-0">
                                        {/* PRIMARY DATA: Degree name + University name (bold and prominent) */}
                                        <h4 class="text-sky-900 font-bold text-lg mb-1">{degree.degree}</h4>
                                        <h5 class="text-sky-900 font-bold text-base mb-2">{degree.institution}</h5>

                                        {/* SECONDARY DATA: Year completed, intake year, additional details (smaller and less prominent) */}
                                        <div class="text-sm text-slate-600 mb-2 space-y-1">
                                            {degree.yearCompleted && (
                                                <p>
                                                    <span class="font-medium">Year Completed:</span>{" "}
                                                    {degree.yearCompleted}
                                                </p>
                                            )}
                                            <p>
                                                <span class="font-medium">Type:</span> {degree.ugpg}
                                            </p>
                                        </div>

                                        {degree.details && degree.details.length > 0 && (
                                            <ul class="list-disc pl-5 mt-2 text-sm space-y-1">
                                                {degree.details.map((detail) => (
                                                    <li>{detail}</li>
                                                ))}
                                            </ul>
                                        )}
                                    </div>
                                </div>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Professional Certifications -->
        {
            certifications.length > 0 && (
                <section aria-labelledby="certs-title" class="border-t border-slate-100 pt-6">
                    <h3 id="certs-title" class="text-sky-900 font-bold">
                        {sectionTitles.professionalCertifications}
                    </h3>
                    <div class="mt-3 grid gap-4">
                        {certifications.map((cert) => {
                            const IconComponent = getCertificationIconComponent(cert.icon);
                            return (
                                <article class="bg-white border border-slate-200 rounded p-4">
                                    <div class="flex items-start gap-4">
                                        {/* Certification Icon */}
                                        <div class="flex-shrink-0 mt-1">
                                            <IconComponent class="w-6 h-6 text-sky-600" />
                                        </div>

                                        <div class="flex-1 min-w-0">
                                            {/* PRIMARY DATA: Certification title + Year (bold and prominent) */}
                                            <h4 class="text-sky-900 font-bold text-lg mb-1">{cert.title}</h4>
                                            <h5 class="text-sky-900 font-bold text-base mb-2">{cert.issued}</h5>

                                            {/* SECONDARY DATA: Issuing organization, location (smaller and less prominent) */}
                                            <div class="text-sm text-slate-600 space-y-1">
                                                <p>
                                                    <span class="font-medium">Issuer:</span> {cert.issuer}
                                                </p>
                                                <p>
                                                    <span class="font-medium">Location:</span> {cert.location}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            );
                        })}
                    </div>
                </section>
            )
        }
    </div>
</section>
